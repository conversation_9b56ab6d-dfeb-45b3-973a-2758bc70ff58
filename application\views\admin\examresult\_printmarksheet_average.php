<?php if (!defined('BASEPATH')) exit('No direct script access allowed'); ?>
<!DOCTYPE html>
<html>
<head>
	<title>Average Passing Marksheet</title>
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/bootstrap/css/bootstrap.min.css" />
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/dist/css/font-awesome.min.css" />
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/dist/css/ionicons.min.css" />
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/dist/css/AdminLTE.min.css" />
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/dist/css/skins/_all-skins.min.css" />
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/plugins/iCheck/flat/blue.css" />
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/plugins/morris/morris.css" />
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/plugins/jvectormap/jquery-jvectormap-1.2.2.css" />
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/plugins/datepicker/datepicker3.css" />
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/plugins/daterangepicker/daterangepicker-bs3.css" />
	<style type="text/css">
		@media print {
			.pagebreak { clear: both; page-break-after: always; }
			@page { size: A4; margin: 0; }
			.marksheet { margin: 0; border: initial; border-radius: initial; width: initial; min-height: initial; box-shadow: initial; background: initial; page-break-after: always; }
		}
		.subject_name { background: #ffffff !important; color: #000 !important; }
		table.marks th { color: #fff !important; }
		.overall { background-color: #ffd3a0; }
		.denifittable_marks table tr td { padding: 10px; }
		.overall_mark_details td:first-child { padding: 10px; background-color: #ffd3a0; }
		.mark_range { background: #ffd3a0 !important; border-color: #000 !important; font-size: 10px; }
		.pass { color: green; }
		.fail { color: red; }
	</style>
</head>
<body onload="window.print()">
	<div class="marksheet">
		<?php if($template->header_image): ?>
		<div style="margin-bottom: 10px;">
			<img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template->header_image); ?>" width="100%" height="120px" />
		</div>
		<?php endif; ?>

		<div style="margin: 0 auto; width: 98%;">
			<table cellpadding="0" cellspacing="0" width="100%">
				<tbody>
					<tr>
						<td valign="top" style="font-size: 16px; font-weight: 500; text-align: center;">
							<div class="exam-title">EXAM: <?php echo $exam->exam; ?></div>
						</td>
					</tr>
					<tr style="margin-top: 10px;">
						<td>
							<table class="table table-bordered">
								<tbody>
									<tr>
										<td>
											<div>Class: <span style="font-weight: 500;"><?php echo $class_name; ?></span></div>
										</td>
										<td>
											<div>Section: <span style="font-weight: 500;"><?php echo $section_name; ?></span></div>
										</td>
									</tr>
								</tbody>
							</table>
						</td>
					</tr>
					<tr>
						<td valign="top">
							<table width="96%" cellpadding="0" cellspacing="0" class="denifittable marks" style="text-align: center; margin: 0 auto;">
								<thead>
									<tr style="font-size: 10px; font-weight: 400;">
										<th>Subject</th>
										<th>Average Marks</th>
										<th>Passing Percentage</th>
										<th>Status</th>
									</tr>
								</thead>
								<tbody>
									<?php foreach ($subjects as $subject): ?>
									<tr>
										<td class="subject_name"><?php echo $subject['name']; ?></td>
										<td class="subject_name"><?php echo number_format($subject['average_marks'], 2); ?></td>
										<td class="subject_name"><?php echo number_format($subject['pass_percentage'], 2); ?>%</td>
										<td class="subject_name <?php echo ($subject['average_marks'] >= $subject['pass_marks']) ? 'pass' : 'fail'; ?>" style="font-weight: bold;">
											<?php echo ($subject['average_marks'] >= $subject['pass_marks']) ? 'PASS' : 'FAIL'; ?>
										</td>
									</tr>
									<?php endforeach; ?>
								</tbody>
							</table>
						</td>
					</tr>
				</tbody>
			</table>

			<table class="table-bordered denifittable_marks" cellpadding="0" cellspacing="0" width="100%" style="text-align: center; margin-top: 20px; text-transform: uppercase;">
				<tbody>
					<tr class="overall_mark_details">
						<td style="font-size: 14px;">
							Class Average
						</td>
						<td><b><?php echo number_format($class_average, 2); ?>%</b></td>
						<td style="" class="text-center">
							Pass Rate
						</td>
						<td>
							<b><?php echo number_format($pass_rate, 2); ?>%</b>
						</td>
					</tr>
				</tbody>
			</table>

			<table class="table table-bordered" valign="top" cellpadding="0" cellspacing="0" style="margin-top: 20px;">
				<tbody>
					<tr>
						<td width="20%" style="margin: 0 auto; vertical-align: middle;" rowspan="2">
							<div style="font-size: 20px; font-weight: 600; text-transform: uppercase; border-bottom: 1px solid; width: 200px; text-align: center;">DATE</div>
							<div style="width: 200px; text-align: center;"><?php echo date('d-m-Y'); ?></div>
						</td>
						<td style="height: 45px;" valign="bottom"></td>
						<td style="height: 45px;" valign="bottom">
							<?php if($template->signature): ?>
								<img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template->signature); ?>" width="200" height="50" />
							<?php endif; ?>
						</td>
					</tr>
					<tr>
						<td style="font-size: 12px;">
							SIGNATURE OF CLASS TEACHER
						</td>
						<td style="font-size: 12px;">
							SIGNATURE OF PRINCIPAL
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</body>
</html>
