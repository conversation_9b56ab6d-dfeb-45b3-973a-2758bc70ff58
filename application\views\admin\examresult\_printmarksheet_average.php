<?php if (!defined('BASEPATH')) exit('No direct script access allowed'); ?>
<!DOCTYPE html>
<html>
<head>
	<title><?php echo isset($template->title) && !empty($template->title) ? $template->title : 'Average Passing Marksheet'; ?></title>
	<link rel="stylesheet" href="<?php echo base_url(); ?>backend/pdf_style.css?1">
	<style type="text/css">
		@media print {
			.pagebreak { clear: both; page-break-after: always; }
			@page { size: A4; margin: 0; }
			.marksheet { margin: 0; border: initial; border-radius: initial; width: initial; min-height: initial; box-shadow: initial; background: initial; page-break-after: always; }
		}

		/* Template-based styling */
		.subject_name {
			background: <?php echo isset($template->bgcolor) && $template->bgcolor != '#FFFFFF' ? $template->bgcolor : '#ffffff'; ?> !important;
			color: <?php echo isset($template->bgcolor) && $template->bgcolor != '#FFFFFF' ? '#fff' : '#000'; ?> !important;
		}

		table.marks th {
			color: #fff !important;
			background-color: <?php echo isset($template->bgcolor) && $template->bgcolor != '#FFFFFF' ? $template->bgcolor : '#2245ffcf'; ?> !important;
		}

		.overall { background-color: #ffd3a0; }
		.denifittable_marks table tr td { padding: 10px; }
		.overall_mark_details td:first-child, .overall_mark_details td:nth-child(3) {
			padding: 10px;
			background-color: #ffd3a0 !important;
			width: 20%;
			color: #000 !important;
		}
		.mark_range { background: #ffd3a0 !important; border-color: #000 !important; font-size: 10px; }
		.pass { color: green; font-weight: bold; }
		.fail { color: red; font-weight: bold; }

		/* Enhanced styling */
		.exam-title {
			font-size: 18px;
			font-weight: bold;
			text-transform: uppercase;
			margin-bottom: 10px;
			color: <?php echo isset($template->bgcolor) && $template->bgcolor != '#FFFFFF' ? $template->bgcolor : '#333'; ?>;
		}

		.school-header {
			text-align: center;
			margin-bottom: 20px;
		}

		.school-name {
			font-size: 24px;
			font-weight: bold;
			margin-bottom: 5px;
		}

		.school-address {
			font-size: 14px;
			color: #666;
		}

		.marksheet-title {
			font-size: 20px;
			font-weight: bold;
			text-align: center;
			margin: 15px 0;
			text-transform: uppercase;
			border-bottom: 2px solid <?php echo isset($template->bgcolor) && $template->bgcolor != '#FFFFFF' ? $template->bgcolor : '#333'; ?>;
			padding-bottom: 5px;
		}
	</style>
</head>
<body onload="window.print()">
	<div class="marksheet" style="background:url('<?php echo isset($template->background_img) && !empty($template->background_img) ? $this->media_storage->getImageURL('uploads/marksheet/' . $template->background_img) : ''; ?>') center center;background-repeat: no-repeat;background-size: 25%;">

		<?php if(isset($template->header_image) && !empty($template->header_image)): ?>
		<div style="margin-bottom: 10px;">
			<img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template->header_image); ?>" width="100%" height="120px" />
		</div>
		<?php endif; ?>

		<?php if(isset($template->heading) && !empty($template->heading)): ?>
		<div class="school-header">
			<div class="school-name"><?php echo $template->heading; ?></div>
		</div>
		<?php endif; ?>

		<?php if(isset($template->title) && !empty($template->title)): ?>
		<div class="marksheet-title"><?php echo $template->title; ?></div>
		<?php endif; ?>

		<div style="margin: 0 auto; width: 98%;">
			<table cellpadding="0" cellspacing="0" width="100%">
				<tbody>
					<tr>
						<td valign="top" style="text-align: center;">
							<div class="exam-title">
								<?php if(isset($template->exam_session) && $template->exam_session == 1): ?>
									EXAM: <?php echo $exam->exam; ?>
								<?php endif; ?>
							</div>
						</td>
					</tr>
					<tr style="margin-top: 10px;">
						<td>
							<table class="table table-bordered">
								<tbody>
									<tr>
										<?php if(isset($template->is_class) && $template->is_class == 1): ?>
										<td>
											<div>Class: <span style="font-weight: 500;"><?php echo $class_name; ?></span></div>
										</td>
										<?php endif; ?>
										<?php if(isset($template->is_section) && $template->is_section == 1): ?>
										<td>
											<div>Section: <span style="font-weight: 500;"><?php echo $section_name; ?></span></div>
										</td>
										<?php endif; ?>
									</tr>
								</tbody>
							</table>
						</td>
					</tr>
					<tr>
						<td valign="top">
							<table width="96%" cellpadding="0" cellspacing="0" class="denifittable marks" style="text-align: center; margin: 0 auto; border-collapse: collapse;">
								<thead>
									<tr style="font-size: 12px; font-weight: bold;">
										<th style="border: 1px solid #000; padding: 10px;">Subject</th>
										<th style="border: 1px solid #000; padding: 10px;">Average Marks</th>
										<?php if(isset($template->is_show_max_marks) && $template->is_show_max_marks == 1): ?>
										<th style="border: 1px solid #000; padding: 10px;">Max Marks</th>
										<?php endif; ?>
										<th style="border: 1px solid #000; padding: 10px;">Passing %</th>
										<?php if(isset($template->is_grade) && $template->is_grade == 1): ?>
										<th style="border: 1px solid #000; padding: 10px;">Grade</th>
										<?php endif; ?>
										<th style="border: 1px solid #000; padding: 10px;">Status</th>
									</tr>
								</thead>
								<tbody>
									<?php foreach ($subjects as $subject): ?>
									<tr>
										<td class="subject_name" style="border: 1px solid #000; padding: 8px; text-align: left;"><?php echo $subject['name']; ?></td>
										<td class="subject_name" style="border: 1px solid #000; padding: 8px;"><?php echo number_format($subject['average_marks'], 2); ?></td>
										<?php if(isset($template->is_show_max_marks) && $template->is_show_max_marks == 1): ?>
										<td class="subject_name" style="border: 1px solid #000; padding: 8px;"><?php echo isset($subject['max_marks']) ? $subject['max_marks'] : 'N/A'; ?></td>
										<?php endif; ?>
										<td class="subject_name" style="border: 1px solid #000; padding: 8px;"><?php echo number_format($subject['pass_percentage'], 2); ?>%</td>
										<?php if(isset($template->is_grade) && $template->is_grade == 1): ?>
										<td class="subject_name" style="border: 1px solid #000; padding: 8px;"><?php echo isset($subject['grade']) ? $subject['grade'] : 'N/A'; ?></td>
										<?php endif; ?>
										<td class="subject_name <?php echo ($subject['average_marks'] >= $subject['pass_marks']) ? 'pass' : 'fail'; ?>" style="border: 1px solid #000; padding: 8px; font-weight: bold;">
											<?php echo ($subject['average_marks'] >= $subject['pass_marks']) ? 'PASS' : 'FAIL'; ?>
										</td>
									</tr>
									<?php endforeach; ?>
								</tbody>
							</table>
						</td>
					</tr>
				</tbody>
			</table>

			<!-- Overall Statistics -->
			<table class="table-bordered denifittable_marks" cellpadding="0" cellspacing="0" width="100%" style="text-align: center; margin-top: 20px; border-collapse: collapse;">
				<tbody>
					<tr class="overall_mark_details">
						<td style="font-size: 14px; border: 1px solid #000; padding: 10px; font-weight: bold;">
							Class Average
						</td>
						<td style="border: 1px solid #000; padding: 10px;"><b><?php echo number_format($class_average, 2); ?>%</b></td>
						<td style="font-size: 14px; border: 1px solid #000; padding: 10px; font-weight: bold;">
							Pass Rate
						</td>
						<td style="border: 1px solid #000; padding: 10px;">
							<b><?php echo number_format($pass_rate, 2); ?>%</b>
						</td>
					</tr>
					<?php if(isset($total_students)): ?>
					<tr class="overall_mark_details">
						<td style="font-size: 14px; border: 1px solid #000; padding: 10px; font-weight: bold;">
							Total Students
						</td>
						<td style="border: 1px solid #000; padding: 10px;"><b><?php echo $total_students; ?></b></td>
						<td style="font-size: 14px; border: 1px solid #000; padding: 10px; font-weight: bold;">
							Students Passed
						</td>
						<td style="border: 1px solid #000; padding: 10px;">
							<b><?php echo isset($students_passed) ? $students_passed : round(($pass_rate * $total_students) / 100); ?></b>
						</td>
					</tr>
					<?php endif; ?>
				</tbody>
			</table>

			<!-- Custom Content -->
			<?php if(isset($template->content) && !empty($template->content)): ?>
			<div style="margin-top: 20px; padding: 15px; border: 1px solid #ddd; background-color: #f9f9f9;">
				<div style="font-weight: bold; margin-bottom: 10px;">Additional Information:</div>
				<div><?php echo $template->content; ?></div>
			</div>
			<?php endif; ?>

			<!-- Signature Section -->
			<table class="table table-bordered" valign="top" cellpadding="0" cellspacing="0" style="margin-top: 30px; border-collapse: collapse;">
				<tbody>
					<tr>
						<td width="25%" style="margin: 0 auto; vertical-align: middle; border: 1px solid #000; padding: 15px; text-align: center;" rowspan="2">
							<?php if(isset($template->date) && !empty($template->date)): ?>
								<div style="font-size: 16px; font-weight: 600; text-transform: uppercase; border-bottom: 1px solid; margin-bottom: 10px;">DATE</div>
								<div><?php echo $template->date; ?></div>
							<?php else: ?>
								<div style="font-size: 16px; font-weight: 600; text-transform: uppercase; border-bottom: 1px solid; margin-bottom: 10px;">DATE</div>
								<div><?php echo date('d-m-Y'); ?></div>
							<?php endif; ?>
						</td>

						<td style="height: 60px; border: 1px solid #000; padding: 10px;" valign="bottom">
							<?php if(isset($template->left_sign) && !empty($template->left_sign)): ?>
								<img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template->left_sign); ?>" width="150" height="40" />
							<?php endif; ?>
						</td>

						<?php if(isset($template->middle_sign) && !empty($template->middle_sign)): ?>
						<td style="height: 60px; border: 1px solid #000; padding: 10px;" valign="bottom">
							<img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template->middle_sign); ?>" width="150" height="40" />
						</td>
						<?php endif; ?>

						<td style="height: 60px; border: 1px solid #000; padding: 10px;" valign="bottom">
							<?php if(isset($template->right_sign) && !empty($template->right_sign)): ?>
								<img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template->right_sign); ?>" width="150" height="40" />
							<?php endif; ?>
						</td>
					</tr>
					<tr>
						<td style="font-size: 12px; border: 1px solid #000; padding: 10px; text-align: center; font-weight: bold;">
							CLASS TEACHER
						</td>

						<?php if(isset($template->middle_sign) && !empty($template->middle_sign)): ?>
						<td style="font-size: 12px; border: 1px solid #000; padding: 10px; text-align: center; font-weight: bold;">
							VICE PRINCIPAL
						</td>
						<?php endif; ?>

						<td style="font-size: 12px; border: 1px solid #000; padding: 10px; text-align: center; font-weight: bold;">
							PRINCIPAL
						</td>
					</tr>
				</tbody>
			</table>

			<!-- Footer Content -->
			<?php if(isset($template->content_footer) && !empty($template->content_footer)): ?>
			<div style="margin-top: 20px; text-align: center; font-size: 12px; color: #666;">
				<?php echo $template->content_footer; ?>
			</div>
			<?php endif; ?>
		</div>
	</div>
</body>
</html>
